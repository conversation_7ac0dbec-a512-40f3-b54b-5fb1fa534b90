<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".ServerListActivity">

    <!-- Top App Bar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        app:title="@string/server_list_title"
        app:titleTextColor="@color/white"
        app:navigationIcon="@drawable/ic_arrow_back"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Server Type Tabs -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        app:tabTextColor="@color/text_secondary"
        app:tabSelectedTextColor="@color/primary_blue"
        app:tabIndicatorColor="@color/primary_blue"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/free_servers" />

        <com.google.android.material.tabs.TabItem
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/premium_servers" />

    </com.google.android.material.tabs.TabLayout>

    <!-- Server List -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/serverRecyclerView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:padding="8dp"
        app:layout_constraintTop_toBottomOf="@id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:listitem="@layout/item_server" />

    <!-- Loading Progress -->
    <com.google.android.material.progressindicator.CircularProgressIndicator
        android:id="@+id/loadingProgress"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="@id/serverRecyclerView"
        app:layout_constraintBottom_toBottomOf="@id/serverRecyclerView"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
