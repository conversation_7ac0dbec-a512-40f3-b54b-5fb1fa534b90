package com.vpnturbo.app

import android.content.Intent
import android.net.VpnService
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.vpnturbo.app.data.ServerRepository
import com.vpnturbo.app.databinding.ActivityMainBinding
import com.vpnturbo.app.model.ConnectionStatus
import com.vpnturbo.app.model.VPNServer
import com.vpnturbo.app.service.VPNService
import kotlinx.coroutines.launch
import java.util.Timer
import java.util.TimerTask

class MainActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityMainBinding
    private val serverRepository = ServerRepository()
    private var selectedServer: VPNServer? = null
    private var sessionTimer: Timer? = null
    private var sessionStartTime: Long = 0
    
    private val vpnPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            connectToVPN()
        } else {
            Toast.makeText(this, getString(R.string.vpn_permission_required), Toast.LENGTH_LONG).show()
        }
    }
    
    private val serverSelectionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            val serverId = result.data?.getStringExtra("selected_server_id")
            serverId?.let { id ->
                lifecycleScope.launch {
                    selectedServer = serverRepository.getServerById(id)
                    updateServerSelection()
                }
            }
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        setupVPNListener()
        loadFastestServer()
    }
    
    private fun setupUI() {
        binding.connectButton.setOnClickListener {
            when (VPNService.connectionStatus) {
                ConnectionStatus.DISCONNECTED -> requestVPNPermission()
                ConnectionStatus.CONNECTED -> disconnectVPN()
                else -> { /* Do nothing during connecting/disconnecting */ }
            }
        }
        
        binding.selectServerButton.setOnClickListener {
            val intent = Intent(this, ServerListActivity::class.java)
            serverSelectionLauncher.launch(intent)
        }
        
        updateConnectionStatus(VPNService.connectionStatus, VPNService.connectedServer)
    }
    
    private fun setupVPNListener() {
        VPNService.connectionListener = { status, server ->
            runOnUiThread {
                updateConnectionStatus(status, server)
            }
        }
    }
    
    private fun loadFastestServer() {
        lifecycleScope.launch {
            try {
                selectedServer = serverRepository.getFastestServer()
                updateServerSelection()
            } catch (e: Exception) {
                Toast.makeText(this@MainActivity, "Failed to load servers", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun updateServerSelection() {
        selectedServer?.let { server ->
            binding.selectServerButton.text = server.getDisplayName()
            binding.serverText.text = "${server.getDisplayName()} - ${server.getServerType()}"
        }
    }
    
    private fun requestVPNPermission() {
        val intent = VpnService.prepare(this)
        if (intent != null) {
            vpnPermissionLauncher.launch(intent)
        } else {
            connectToVPN()
        }
    }
    
    private fun connectToVPN() {
        val server = selectedServer
        if (server == null) {
            Toast.makeText(this, "Please select a server first", Toast.LENGTH_SHORT).show()
            return
        }
        
        val intent = Intent(this, VPNService::class.java).apply {
            action = VPNService.ACTION_CONNECT
            putExtra(VPNService.EXTRA_SERVER, server)
        }
        startService(intent)
    }
    
    private fun disconnectVPN() {
        val intent = Intent(this, VPNService::class.java).apply {
            action = VPNService.ACTION_DISCONNECT
        }
        startService(intent)
    }
    
    private fun updateConnectionStatus(status: ConnectionStatus, server: VPNServer?) {
        when (status) {
            ConnectionStatus.DISCONNECTED -> {
                binding.statusText.text = getString(R.string.disconnected)
                binding.statusText.setTextColor(ContextCompat.getColor(this, R.color.status_disconnected))
                binding.connectButton.text = getString(R.string.connect)
                binding.connectButton.backgroundTintList = ContextCompat.getColorStateList(this, R.color.accent_green)
                binding.connectButton.isEnabled = true
                stopSessionTimer()
            }
            ConnectionStatus.CONNECTING -> {
                binding.statusText.text = getString(R.string.connecting)
                binding.statusText.setTextColor(ContextCompat.getColor(this, R.color.status_connecting))
                binding.connectButton.text = getString(R.string.connecting)
                binding.connectButton.backgroundTintList = ContextCompat.getColorStateList(this, R.color.accent_orange)
                binding.connectButton.isEnabled = false
            }
            ConnectionStatus.CONNECTED -> {
                binding.statusText.text = getString(R.string.connected)
                binding.statusText.setTextColor(ContextCompat.getColor(this, R.color.status_connected))
                binding.connectButton.text = getString(R.string.disconnect)
                binding.connectButton.backgroundTintList = ContextCompat.getColorStateList(this, R.color.accent_red)
                binding.connectButton.isEnabled = true
                server?.let {
                    binding.serverText.text = "${it.getDisplayName()} - Connected"
                }
                startSessionTimer()
            }
            ConnectionStatus.DISCONNECTING -> {
                binding.statusText.text = "Disconnecting..."
                binding.statusText.setTextColor(ContextCompat.getColor(this, R.color.status_connecting))
                binding.connectButton.text = "Disconnecting..."
                binding.connectButton.isEnabled = false
            }
            ConnectionStatus.ERROR -> {
                binding.statusText.text = "Connection Error"
                binding.statusText.setTextColor(ContextCompat.getColor(this, R.color.status_error))
                binding.connectButton.text = getString(R.string.retry)
                binding.connectButton.backgroundTintList = ContextCompat.getColorStateList(this, R.color.accent_green)
                binding.connectButton.isEnabled = true
                Toast.makeText(this, getString(R.string.connection_failed), Toast.LENGTH_LONG).show()
            }
        }
    }
    
    private fun startSessionTimer() {
        sessionStartTime = System.currentTimeMillis()
        sessionTimer = Timer()
        sessionTimer?.scheduleAtFixedRate(object : TimerTask() {
            override fun run() {
                runOnUiThread {
                    val sessionTime = (System.currentTimeMillis() - sessionStartTime) / 1000
                    val hours = sessionTime / 3600
                    val minutes = (sessionTime % 3600) / 60
                    val seconds = sessionTime % 60
                    binding.sessionTimeText.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
                }
            }
        }, 0, 1000)
    }
    
    private fun stopSessionTimer() {
        sessionTimer?.cancel()
        sessionTimer = null
        binding.sessionTimeText.text = "00:00:00"
        binding.dataUsageText.text = "0 MB"
    }
    
    override fun onDestroy() {
        super.onDestroy()
        VPNService.connectionListener = null
        stopSessionTimer()
    }
}
