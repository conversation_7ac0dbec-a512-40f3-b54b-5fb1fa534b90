package com.vpnturbo.app

import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.tabs.TabLayout
import com.vpnturbo.app.adapter.ServerAdapter
import com.vpnturbo.app.data.ServerRepository
import com.vpnturbo.app.databinding.ActivityServerListBinding
import com.vpnturbo.app.model.VPNServer
import kotlinx.coroutines.launch

class ServerListActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityServerListBinding
    private lateinit var serverAdapter: ServerAdapter
    private val serverRepository = ServerRepository()
    private var currentServerList: List<VPNServer> = emptyList()
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityServerListBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        setupUI()
        setupRecyclerView()
        loadFreeServers()
    }
    
    private fun setupUI() {
        binding.toolbar.setNavigationOnClickListener {
            finish()
        }
        
        binding.tabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                when (tab?.position) {
                    0 -> loadFreeServers()
                    1 -> loadPremiumServers()
                }
            }
            
            override fun onTabUnselected(tab: TabLayout.Tab?) {}
            override fun onTabReselected(tab: TabLayout.Tab?) {}
        })
    }
    
    private fun setupRecyclerView() {
        serverAdapter = ServerAdapter { server ->
            selectServer(server)
        }
        
        binding.serverRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@ServerListActivity)
            adapter = serverAdapter
        }
    }
    
    private fun loadFreeServers() {
        showLoading(true)
        lifecycleScope.launch {
            try {
                val servers = serverRepository.getFreeServers()
                currentServerList = servers
                serverAdapter.submitList(servers)
                showLoading(false)
            } catch (e: Exception) {
                showLoading(false)
                // Handle error
            }
        }
    }
    
    private fun loadPremiumServers() {
        showLoading(true)
        lifecycleScope.launch {
            try {
                val servers = serverRepository.getPremiumServers()
                currentServerList = servers
                serverAdapter.submitList(servers)
                showLoading(false)
            } catch (e: Exception) {
                showLoading(false)
                // Handle error
            }
        }
    }
    
    private fun selectServer(server: VPNServer) {
        val resultIntent = Intent().apply {
            putExtra("selected_server_id", server.id)
        }
        setResult(RESULT_OK, resultIntent)
        finish()
    }
    
    private fun showLoading(show: Boolean) {
        binding.loadingProgress.visibility = if (show) View.VISIBLE else View.GONE
        binding.serverRecyclerView.visibility = if (show) View.GONE else View.VISIBLE
    }
}
