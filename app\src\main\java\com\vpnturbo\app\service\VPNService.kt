package com.vpnturbo.app.service

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Intent
import android.net.VpnService
import android.os.Build
import android.os.IBinder
import android.os.ParcelFileDescriptor
import androidx.core.app.NotificationCompat
import com.vpnturbo.app.MainActivity
import com.vpnturbo.app.R
import com.vpnturbo.app.model.ConnectionStatus
import com.vpnturbo.app.model.VPNServer
import java.io.FileInputStream
import java.io.FileOutputStream
import java.net.InetSocketAddress
import java.nio.ByteBuffer
import java.nio.channels.DatagramChannel

class VPNService : VpnService() {
    
    companion object {
        private const val NOTIFICATION_ID = 1
        private const val CHANNEL_ID = "VPN_SERVICE_CHANNEL"
        const val ACTION_CONNECT = "com.vpnturbo.app.CONNECT"
        const val ACTION_DISCONNECT = "com.vpnturbo.app.DISCONNECT"
        const val EXTRA_SERVER = "extra_server"
        
        var connectionStatus = ConnectionStatus.DISCONNECTED
        var connectedServer: VPNServer? = null
        var connectionListener: ((ConnectionStatus, VPNServer?) -> Unit)? = null
    }
    
    private var vpnInterface: ParcelFileDescriptor? = null
    private var isRunning = false
    private var vpnThread: Thread? = null
    
    override fun onCreate() {
        super.onCreate()
        createNotificationChannel()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_CONNECT -> {
                val server = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    intent.getParcelableExtra(EXTRA_SERVER, VPNServer::class.java)
                } else {
                    @Suppress("DEPRECATION")
                    intent.getParcelableExtra(EXTRA_SERVER)
                }
                server?.let { connectToVPN(it) }
            }
            ACTION_DISCONNECT -> {
                disconnectVPN()
            }
        }
        return START_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? {
        return null
    }
    
    private fun connectToVPN(server: VPNServer) {
        if (isRunning) {
            disconnectVPN()
        }
        
        updateConnectionStatus(ConnectionStatus.CONNECTING, server)
        
        try {
            // Create VPN interface
            val builder = Builder()
                .setSession("VPN Turbo")
                .addAddress("********", 24)
                .addDnsServer("*******")
                .addDnsServer("*******")
                .addRoute("0.0.0.0", 0)
                .setMtu(1500)
            
            vpnInterface = builder.establish()
            
            if (vpnInterface != null) {
                isRunning = true
                connectedServer = server
                
                startForeground(NOTIFICATION_ID, createNotification(server))
                
                // Start VPN thread
                vpnThread = Thread {
                    runVPN()
                }
                vpnThread?.start()
                
                updateConnectionStatus(ConnectionStatus.CONNECTED, server)
            } else {
                updateConnectionStatus(ConnectionStatus.ERROR, null)
            }
            
        } catch (e: Exception) {
            e.printStackTrace()
            updateConnectionStatus(ConnectionStatus.ERROR, null)
        }
    }
    
    private fun disconnectVPN() {
        updateConnectionStatus(ConnectionStatus.DISCONNECTING, connectedServer)
        
        isRunning = false
        vpnThread?.interrupt()
        vpnInterface?.close()
        vpnInterface = null
        connectedServer = null
        
        stopForeground(true)
        stopSelf()
        
        updateConnectionStatus(ConnectionStatus.DISCONNECTED, null)
    }
    
    private fun runVPN() {
        try {
            val vpnInput = FileInputStream(vpnInterface!!.fileDescriptor)
            val vpnOutput = FileOutputStream(vpnInterface!!.fileDescriptor)
            
            val packet = ByteBuffer.allocate(32767)
            val channel = DatagramChannel.open()
            
            // Simple packet forwarding loop
            while (isRunning && !Thread.currentThread().isInterrupted) {
                packet.clear()
                
                val length = vpnInput.read(packet.array())
                if (length > 0) {
                    packet.limit(length)
                    
                    // Forward packet (simplified implementation)
                    // In a real VPN, you would encrypt and send to VPN server
                    vpnOutput.write(packet.array(), 0, length)
                }
                
                Thread.sleep(10)
            }
            
            channel.close()
            
        } catch (e: Exception) {
            if (isRunning) {
                e.printStackTrace()
                updateConnectionStatus(ConnectionStatus.ERROR, null)
            }
        }
    }
    
    private fun updateConnectionStatus(status: ConnectionStatus, server: VPNServer?) {
        connectionStatus = status
        connectionListener?.invoke(status, server)
    }
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "VPN Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "VPN connection status"
                setShowBadge(false)
            }
            
            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun createNotification(server: VPNServer): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        val disconnectIntent = Intent(this, VPNService::class.java).apply {
            action = ACTION_DISCONNECT
        }
        val disconnectPendingIntent = PendingIntent.getService(
            this, 0, disconnectIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("VPN Turbo Connected")
            .setContentText("Connected to ${server.getDisplayName()}")
            .setSmallIcon(R.drawable.ic_vpn_key)
            .setContentIntent(pendingIntent)
            .addAction(
                R.drawable.ic_close,
                "Disconnect",
                disconnectPendingIntent
            )
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build()
    }
    
    override fun onDestroy() {
        disconnectVPN()
        super.onDestroy()
    }
}
