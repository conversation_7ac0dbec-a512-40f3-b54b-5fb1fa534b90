# 🚀 البدء السريع - تطبيق VPN Turbo

## ✅ المتطلبات الأساسية

قبل البدء، تأكد من توفر:
- **Android Studio** (Arctic Fox أو أحدث)
- **JDK 8** أو أحدث
- **Android SDK 34**
- جهاز Android أو محاكي

## 📱 خطوات سريعة للتشغيل

### 1️⃣ فتح المشروع
```bash
# افتح Android Studio
# اختر "Open an existing project"
# اختر مجلد "vpn turbo"
```

### 2️⃣ إعداد SDK (إذا لزم الأمر)
```bash
# افتح ملف local.properties
# أضف مسار Android SDK:
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
```

### 3️⃣ بناء التطبيق
```bash
# في Terminal داخل Android Studio:
./gradlew assembleDebug

# أو استخدم الملف المساعد:
build_and_run.bat
```

### 4️⃣ تشغيل التطبيق
- اضغط على زر **Run** في Android Studio
- أو استخدم `Shift + F10`

## 📦 إنشاء ملف APK

### للاختبار (Debug):
```bash
./gradlew assembleDebug
```
📍 الملف في: `app/build/outputs/apk/debug/app-debug.apk`

### للنشر (Release):
```bash
./gradlew assembleRelease
```
📍 الملف في: `app/build/outputs/apk/release/app-release.apk`

## 🔧 حل المشاكل السريع

### ❌ خطأ في البناء؟
```bash
./gradlew clean
./gradlew assembleDebug
```

### ❌ مشكلة في SDK؟
1. افتح **File > Project Structure**
2. تحقق من **SDK Location**
3. تأكد من تثبيت **Android SDK 34**

### ❌ خطأ في Gradle؟
1. **File > Sync Project with Gradle Files**
2. انتظر انتهاء التحميل

## 📋 اختبار التطبيق

### الوظائف الأساسية:
- ✅ فتح التطبيق
- ✅ عرض قائمة الخوادم
- ✅ اختيار خادم
- ✅ طلب إذن VPN
- ✅ محاولة الاتصال
- ✅ عرض الإحصائيات

### الخوادم المتاحة للاختبار:
- 🇺🇸 **الولايات المتحدة** - نيويورك (مجاني)
- 🇬🇧 **المملكة المتحدة** - لندن (مجاني)
- 🇩🇪 **ألمانيا** - فرانكفورت (مجاني)
- 🇯🇵 **اليابان** - طوكيو (مجاني)
- 🇸🇬 **سنغافورة** (مجاني)

## 🎯 الخطوات التالية

بعد التشغيل الناجح:

1. **اقرأ** `DEVELOPER_GUIDE.md` للتفاصيل الكاملة
2. **خصص** الألوان والنصوص حسب احتياجاتك
3. **أضف** خوادم VPN حقيقية
4. **اختبر** على أجهزة مختلفة
5. **وقع** التطبيق للنشر

## 📞 الدعم

إذا واجهت مشاكل:
- راجع `DEVELOPER_GUIDE.md`
- تحقق من ملفات Log في Android Studio
- تأكد من اتصال الإنترنت

---

**🎉 مبروك! تطبيق VPN Turbo جاهز للاستخدام!**
