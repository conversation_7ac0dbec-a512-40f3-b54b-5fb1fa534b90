@echo off
chcp 65001 >nul
echo ========================================
echo      فحص مشروع VPN Turbo
echo ========================================
echo.

echo 🔍 فحص الملفات الأساسية...

set "missing_files="

if not exist "app\build.gradle" (
    echo ❌ ملف app\build.gradle مفقود
    set "missing_files=1"
) else (
    echo ✅ app\build.gradle موجود
)

if not exist "app\src\main\AndroidManifest.xml" (
    echo ❌ ملف AndroidManifest.xml مفقود
    set "missing_files=1"
) else (
    echo ✅ AndroidManifest.xml موجود
)

if not exist "app\src\main\java\com\vpnturbo\app\MainActivity.kt" (
    echo ❌ ملف MainActivity.kt مفقود
    set "missing_files=1"
) else (
    echo ✅ MainActivity.kt موجود
)

if not exist "app\src\main\java\com\vpnturbo\app\service\VPNService.kt" (
    echo ❌ ملف VPNService.kt مفقود
    set "missing_files=1"
) else (
    echo ✅ VPNService.kt موجود
)

if not exist "app\src\main\res\layout\activity_main.xml" (
    echo ❌ ملف activity_main.xml مفقود
    set "missing_files=1"
) else (
    echo ✅ activity_main.xml موجود
)

if not exist "app\src\main\res\values\strings.xml" (
    echo ❌ ملف strings.xml مفقود
    set "missing_files=1"
) else (
    echo ✅ strings.xml موجود
)

echo.
echo 🔧 فحص إعدادات Gradle...

if not exist "gradle\wrapper\gradle-wrapper.properties" (
    echo ❌ ملف gradle-wrapper.properties مفقود
    set "missing_files=1"
) else (
    echo ✅ gradle-wrapper.properties موجود
)

if not exist "gradlew.bat" (
    echo ❌ ملف gradlew.bat مفقود
    set "missing_files=1"
) else (
    echo ✅ gradlew.bat موجود
)

echo.
if defined missing_files (
    echo ❌ يوجد ملفات مفقودة! راجع الأخطاء أعلاه
    echo.
    echo 💡 نصائح:
    echo - تأكد من استخراج جميع الملفات
    echo - راجع دليل QUICK_START.md
    echo - تأكد من صحة هيكل المجلدات
) else (
    echo ✅ جميع الملفات الأساسية موجودة!
    echo.
    echo 🚀 يمكنك الآن:
    echo 1. فتح المشروع في Android Studio
    echo 2. تشغيل build_and_run.bat
    echo 3. أو تشغيل install_and_run.bat
)

echo.
echo 📋 هيكل المشروع:
echo.
tree /F /A | findstr /V ".git"

echo.
pause
