# دليل المطور - تطبيق VPN Turbo

## البدء السريع

### 1. إعد<PERSON> البيئة
```bash
# تأكد من تثبيت:
# - Android Studio Arctic Fox أو أحدث
# - JDK 8+
# - Android SDK 34
```

### 2. فتح المشروع
1. افتح Android Studio
2. اختر "Open an existing project"
3. اختر مجلد المشروع

### 3. بناء التطبيق
```bash
# Debug build
./gradlew assembleDebug

# Release build  
./gradlew assembleRelease

# أو استخدم الملف المساعد
build_and_run.bat
```

## هيكل الكود

### الملفات الرئيسية:

#### 🏠 MainActivity.kt
- الواجهة الرئيسية للتطبيق
- إدارة حالة الاتصال
- عرض الإحصائيات

#### 📋 ServerListActivity.kt  
- قائمة الخوادم المتاحة
- تبويب الخوادم المجانية/المدفوعة
- اختيار الخادم

#### 🔧 VPNService.kt
- خدمة VPN الأساسية
- إدارة الاتصال والقطع
- الإشعارات

#### 📊 ServerRepository.kt
- مستودع بيانات الخوادم
- خوادم وهمية للاختبار
- إدارة قوائم الخوادم

#### 🎨 ServerAdapter.kt
- محول RecyclerView للخوادم
- عرض معلومات الخادم
- معالجة النقرات

## إضافة خوادم جديدة

### خوادم مجانية:
```kotlin
// في ServerRepository.kt
private val freeServers = listOf(
    VPNServer(
        id = "new_server_id",
        name = "اسم الخادم الجديد", 
        country = "اسم الدولة",
        countryCode = "رمز الدولة", // مثل "SA"
        city = "اسم المدينة",
        host = "server.example.com",
        port = 1194,
        isPremium = false,
        ping = 50 // البينغ بالميلي ثانية
    ),
    // ... خوادم أخرى
)
```

### إضافة علم دولة جديد:
1. أنشئ ملف في `res/drawable/flag_xx.xml` (xx = رمز الدولة)
2. أضف الرمز في `ServerAdapter.kt`:
```kotlin
private fun getFlagResource(countryCode: String): Int {
    return when (countryCode.uppercase()) {
        "SA" -> R.drawable.flag_sa
        // ... دول أخرى
        else -> R.drawable.flag_default
    }
}
```

## تخصيص التصميم

### الألوان:
عدّل `res/values/colors.xml`:
```xml
<color name="primary_blue">#2196F3</color>
<color name="accent_green">#4CAF50</color>
<!-- ألوان أخرى -->
```

### النصوص:
عدّل `res/values/strings.xml`:
```xml
<string name="app_name">VPN Turbo</string>
<string name="connect">اتصال</string>
<!-- نصوص أخرى -->
```

### التخطيطات:
- `activity_main.xml` - الواجهة الرئيسية
- `activity_server_list.xml` - قائمة الخوادم  
- `item_server.xml` - عنصر خادم واحد

## اختبار التطبيق

### 1. اختبار على المحاكي:
```bash
# تشغيل المحاكي
emulator -avd Pixel_4_API_30

# تثبيت التطبيق
adb install app/build/outputs/apk/debug/app-debug.apk
```

### 2. اختبار على جهاز حقيقي:
1. فعّل "خيارات المطور" على الجهاز
2. فعّل "تصحيح USB"
3. وصل الجهاز بالكمبيوتر
4. ثبت التطبيق

### 3. اختبار الوظائف:
- ✅ اختيار خادم
- ✅ طلب إذن VPN
- ✅ الاتصال والقطع
- ✅ عرض الإحصائيات
- ✅ الإشعارات

## حل المشاكل الشائعة

### مشكلة: فشل البناء
```bash
# نظف المشروع
./gradlew clean

# أعد البناء
./gradlew assembleDebug
```

### مشكلة: خطأ في SDK
1. افتح `local.properties`
2. تأكد من مسار SDK الصحيح:
```
sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
```

### مشكلة: خطأ في الصلاحيات
تأكد من وجود جميع الصلاحيات في `AndroidManifest.xml`

### مشكلة: فشل اتصال VPN
- تحقق من إعدادات الشبكة
- تأكد من منح إذن VPN
- جرب خادم آخر

## نشر التطبيق

### 1. إنشاء مفتاح التوقيع:
```bash
keytool -genkey -v -keystore vpn-turbo-key.keystore -alias vpn-turbo -keyalg RSA -keysize 2048 -validity 10000
```

### 2. توقيع التطبيق:
أضف في `app/build.gradle`:
```gradle
android {
    signingConfigs {
        release {
            storeFile file('vpn-turbo-key.keystore')
            storePassword 'your_password'
            keyAlias 'vpn-turbo'
            keyPassword 'your_password'
        }
    }
    buildTypes {
        release {
            signingConfig signingConfigs.release
            // ...
        }
    }
}
```

### 3. بناء النسخة النهائية:
```bash
./gradlew assembleRelease
```

## نصائح للتطوير

### 1. استخدم Logcat للتصحيح:
```kotlin
Log.d("VPNTurbo", "رسالة تصحيح")
Log.e("VPNTurbo", "رسالة خطأ")
```

### 2. اختبر على أجهزة مختلفة:
- أحجام شاشة مختلفة
- إصدارات Android مختلفة
- شركات مختلفة

### 3. راقب الأداء:
- استخدام الذاكرة
- استهلاك البطارية
- سرعة الشبكة

### 4. اتبع أفضل الممارسات:
- استخدم ViewBinding
- اتبع نمط MVVM
- اكتب تعليقات واضحة
- اختبر الكود

## الموارد المفيدة

- [دليل Android Developer](https://developer.android.com/)
- [مرجع Kotlin](https://kotlinlang.org/docs/)
- [Material Design](https://material.io/design)
- [OpenVPN Documentation](https://openvpn.net/community-resources/)

---

**ملاحظة:** هذا التطبيق للأغراض التعليمية. تأكد من الامتثال للقوانين المحلية.
