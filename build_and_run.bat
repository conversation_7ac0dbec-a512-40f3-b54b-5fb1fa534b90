@echo off
echo ========================================
echo       بناء وتشغيل تطبيق VPN Turbo
echo ========================================
echo.

echo جاري بناء المشروع...
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo.
    echo ملف APK متوفر في:
    echo app\build\outputs\apk\debug\app-debug.apk
    echo.
    echo لتثبيت التطبيق على الجهاز المتصل:
    echo adb install app\build\outputs\apk\debug\app-debug.apk
    echo.
) else (
    echo.
    echo ❌ فشل في بناء التطبيق!
    echo تحقق من الأخطاء أعلاه
    echo.
)

pause
