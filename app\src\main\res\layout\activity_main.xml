<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_light"
    tools:context=".MainActivity">

    <!-- Top App Bar -->
    <com.google.android.material.appbar.MaterialToolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="@color/primary_blue"
        app:title="@string/app_name"
        app:titleTextColor="@color/white"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Connection Status Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/statusCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="8dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <TextView
                android:id="@+id/statusText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="@string/disconnected"
                android:textColor="@color/status_disconnected"
                android:textSize="18sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/serverText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="8dp"
                android:text="@string/select_server"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Connect Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/connectButton"
        style="@style/ConnectButton"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:backgroundTint="@color/accent_green"
        android:text="@string/connect"
        android:textColor="@color/white"
        app:layout_constraintBottom_toTopOf="@id/serverSelectionCard"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/statusCard" />

    <!-- Server Selection Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/serverSelectionCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toTopOf="@id/statsCard">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/select_server"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/selectServerButton"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="@string/fastest_server"
                android:textColor="@color/primary_blue" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Stats Card -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/statsCard"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        app:cardCornerRadius="16dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="16dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/session_time"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/sessionTimeText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="00:00:00"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/data_usage"
                    android:textColor="@color/text_secondary"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/dataUsageText"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0 MB"
                    android:textColor="@color/text_primary"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

</androidx.constraintlayout.widget.ConstraintLayout>
