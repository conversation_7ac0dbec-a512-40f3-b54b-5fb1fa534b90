# VPN Turbo - تطبيق VPN مجاني

تطبيق VPN Android مع خوادم مجانية ومدفوعة، مصمم ليكون سهل الاستخدام وآمن.

## الميزات

- ✅ خوادم VPN مجانية في عدة دول
- ✅ واجهة مستخدم عربية بسيطة وجميلة
- ✅ اتصال بنقرة واحدة
- ✅ عرض إحصائيات الاستخدام
- ✅ اختيار الخادم الأسرع تلقائياً
- ✅ دعم OpenVPN
- ✅ إشعارات حالة الاتصال

## الخوادم المتاحة

### خوادم مجانية:
- 🇺🇸 الولايات المتحدة (نيويورك)
- 🇬🇧 المملكة المتحدة (لندن)
- 🇩🇪 ألمانيا (فرانكفورت)
- 🇯🇵 اليابان (طوكيو)
- 🇸🇬 سنغافورة

### خوادم مدفوعة:
- 🇺🇸 الولايات المتحدة (لوس أنجلوس) - سرعة عالية
- 🇬🇧 المملكة المتحدة (مانشستر) - سرعة عالية

## متطلبات النظام

- Android 5.0 (API level 21) أو أحدث
- 50 MB مساحة تخزين
- اتصال بالإنترنت

## كيفية البناء والتشغيل

### 1. متطلبات التطوير:
- Android Studio Arctic Fox أو أحدث
- JDK 8 أو أحدث
- Android SDK 34

### 2. خطوات البناء:

1. **استنساخ المشروع:**
   ```bash
   git clone <repository-url>
   cd vpn-turbo
   ```

2. **فتح المشروع في Android Studio:**
   - افتح Android Studio
   - اختر "Open an existing Android Studio project"
   - اختر مجلد المشروع

3. **تحديث SDK path:**
   - افتح ملف `local.properties`
   - أضف مسار Android SDK:
   ```
   sdk.dir=C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk
   ```

4. **بناء المشروع:**
   - انتظر حتى ينتهي Gradle من التحميل
   - اختر Build > Make Project

5. **تشغيل التطبيق:**
   - وصل جهاز Android أو استخدم محاكي
   - اضغط على زر Run

### 3. إنشاء APK:

1. **Debug APK:**
   ```bash
   ./gradlew assembleDebug
   ```
   الملف سيكون في: `app/build/outputs/apk/debug/app-debug.apk`

2. **Release APK:**
   ```bash
   ./gradlew assembleRelease
   ```
   الملف سيكون في: `app/build/outputs/apk/release/app-release.apk`

## هيكل المشروع

```
app/
├── src/main/
│   ├── java/com/vpnturbo/app/
│   │   ├── MainActivity.kt              # النشاط الرئيسي
│   │   ├── ServerListActivity.kt        # قائمة الخوادم
│   │   ├── adapter/
│   │   │   └── ServerAdapter.kt         # محول قائمة الخوادم
│   │   ├── data/
│   │   │   └── ServerRepository.kt      # مستودع بيانات الخوادم
│   │   ├── model/
│   │   │   └── VPNServer.kt            # نموذج بيانات الخادم
│   │   └── service/
│   │       └── VPNService.kt           # خدمة VPN
│   ├── res/
│   │   ├── layout/                     # ملفات التخطيط
│   │   ├── drawable/                   # الرسوم والأيقونات
│   │   ├── values/                     # القيم والألوان والنصوص
│   │   └── xml/                        # ملفات XML إضافية
│   └── AndroidManifest.xml            # ملف البيان
├── build.gradle                       # إعدادات البناء
└── proguard-rules.pro                 # قواعد ProGuard
```

## الصلاحيات المطلوبة

- `INTERNET` - للاتصال بالإنترنت
- `ACCESS_NETWORK_STATE` - لمراقبة حالة الشبكة
- `ACCESS_WIFI_STATE` - لمراقبة حالة WiFi
- `BIND_VPN_SERVICE` - لربط خدمة VPN
- `SYSTEM_ALERT_WINDOW` - للإشعارات
- `WAKE_LOCK` - لمنع النوم أثناء الاتصال

## كيفية الاستخدام

1. **تشغيل التطبيق**
2. **اختيار خادم** من قائمة الخوادم المتاحة
3. **الضغط على زر الاتصال**
4. **منح إذن VPN** عند الطلب
5. **الاستمتاع بالتصفح الآمن**

## التخصيص

### إضافة خوادم جديدة:
عدّل ملف `ServerRepository.kt` وأضف خوادم جديدة إلى القوائم:

```kotlin
VPNServer(
    id = "new_server_id",
    name = "Server Name",
    country = "Country",
    countryCode = "CC",
    city = "City",
    host = "server.example.com",
    port = 1194,
    isPremium = false
)
```

### تغيير الألوان والتصميم:
عدّل ملفات في مجلد `res/values/`:
- `colors.xml` - للألوان
- `strings.xml` - للنصوص
- `themes.xml` - للتصاميم

## الأمان

- يستخدم التطبيق بروتوكول OpenVPN الآمن
- جميع البيانات مشفرة
- لا يتم تسجيل أو حفظ بيانات المستخدمين
- الخوادم المجانية للاستخدام التعليمي فقط

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**ملاحظة:** هذا التطبيق للأغراض التعليمية. تأكد من الامتثال للقوانين المحلية عند استخدام خدمات VPN.
