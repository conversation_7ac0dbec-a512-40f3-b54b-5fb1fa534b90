<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    style="@style/ServerItemCard"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?attr/selectableItemBackground">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Country Flag -->
        <ImageView
            android:id="@+id/flagImageView"
            android:layout_width="32dp"
            android:layout_height="24dp"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/flag_us" />

        <!-- Country Name -->
        <TextView
            android:id="@+id/countryNameText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintStart_toEndOf="@id/flagImageView"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/pingText"
            tools:text="United States" />

        <!-- Server Name -->
        <TextView
            android:id="@+id/serverNameText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:textColor="@color/text_secondary"
            android:textSize="14sp"
            app:layout_constraintStart_toEndOf="@id/flagImageView"
            app:layout_constraintTop_toBottomOf="@id/countryNameText"
            app:layout_constraintEnd_toStartOf="@id/pingText"
            tools:text="New York - Free" />

        <!-- Ping -->
        <TextView
            android:id="@+id/pingText"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/accent_green"
            android:textSize="14sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="45ms" />

        <!-- Signal Strength -->
        <ImageView
            android:id="@+id/signalImageView"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginTop="4dp"
            android:src="@drawable/ic_signal_3"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/pingText"
            app:tint="@color/accent_green" />

        <!-- Premium Badge -->
        <TextView
            android:id="@+id/premiumBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:background="@drawable/badge_premium"
            android:paddingHorizontal="8dp"
            android:paddingVertical="2dp"
            android:text="PRO"
            android:textColor="@color/white"
            android:textSize="10sp"
            android:textStyle="bold"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/flagImageView"
            app:layout_constraintTop_toBottomOf="@id/flagImageView"
            tools:visibility="visible" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>
