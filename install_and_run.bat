@echo off
chcp 65001 >nul
echo ========================================
echo    تثبيت وتشغيل تطبيق VPN Turbo
echo ========================================
echo.

echo 🔍 البحث عن الأجهزة المتصلة...
adb devices

echo.
echo 🏗️ بناء التطبيق...
call gradlew.bat assembleDebug

if %ERRORLEVEL% EQU 0 (
    echo.
    echo ✅ تم بناء التطبيق بنجاح!
    echo.
    echo 📱 تثبيت التطبيق على الجهاز...
    adb install -r app\build\outputs\apk\debug\app-debug.apk
    
    if %ERRORLEVEL% EQU 0 (
        echo.
        echo ✅ تم تثبيت التطبيق بنجاح!
        echo.
        echo 🚀 تشغيل التطبيق...
        adb shell am start -n com.vpnturbo.app/.MainActivity
        echo.
        echo 🎉 تم تشغيل التطبيق على الجهاز!
    ) else (
        echo.
        echo ❌ فشل في تثبيت التطبيق!
        echo تأكد من:
        echo - توصيل الجهاز بالكمبيوتر
        echo - تفعيل تصحيح USB
        echo - السماح للكمبيوتر بالوصول للجهاز
    )
) else (
    echo.
    echo ❌ فشل في بناء التطبيق!
    echo راجع الأخطاء أعلاه
)

echo.
pause
