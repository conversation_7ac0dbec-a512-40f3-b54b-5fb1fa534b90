package com.vpnturbo.app.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.vpnturbo.app.R
import com.vpnturbo.app.databinding.ItemServerBinding
import com.vpnturbo.app.model.VPNServer

class ServerAdapter(
    private val onServerClick: (VPNServer) -> Unit
) : ListAdapter<VPNServer, ServerAdapter.ServerViewHolder>(ServerDiffCallback()) {
    
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ServerViewHolder {
        val binding = ItemServerBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ServerViewHolder(binding)
    }
    
    override fun onBindViewHolder(holder: ServerViewHolder, position: Int) {
        holder.bind(getItem(position))
    }
    
    inner class ServerViewHolder(
        private val binding: ItemServerBinding
    ) : RecyclerView.ViewHolder(binding.root) {
        
        fun bind(server: VPNServer) {
            binding.apply {
                countryNameText.text = server.country
                serverNameText.text = "${server.city} - ${server.getServerType()}"
                pingText.text = server.getPingDisplay()
                
                // Set flag (placeholder implementation)
                flagImageView.setImageResource(getFlagResource(server.countryCode))
                
                // Set signal strength
                signalImageView.setImageResource(getSignalResource(server.getSignalStrength()))
                
                // Set ping color based on value
                val pingColor = when {
                    server.ping < 0 -> R.color.text_secondary
                    server.ping < 50 -> R.color.accent_green
                    server.ping < 100 -> R.color.accent_orange
                    else -> R.color.accent_red
                }
                pingText.setTextColor(ContextCompat.getColor(itemView.context, pingColor))
                signalImageView.setColorFilter(ContextCompat.getColor(itemView.context, pingColor))
                
                // Show premium badge
                premiumBadge.visibility = if (server.isPremium) View.VISIBLE else View.GONE
                
                // Set click listener
                root.setOnClickListener {
                    onServerClick(server)
                }
            }
        }
        
        private fun getFlagResource(countryCode: String): Int {
            return when (countryCode.uppercase()) {
                "US" -> R.drawable.flag_us
                "GB", "UK" -> R.drawable.flag_gb
                "DE" -> R.drawable.flag_de
                "JP" -> R.drawable.flag_jp
                "SG" -> R.drawable.flag_sg
                else -> R.drawable.flag_default
            }
        }
        
        private fun getSignalResource(strength: Int): Int {
            return when (strength) {
                4 -> R.drawable.ic_signal_4
                3 -> R.drawable.ic_signal_3
                2 -> R.drawable.ic_signal_2
                1 -> R.drawable.ic_signal_1
                else -> R.drawable.ic_signal_0
            }
        }
    }
    
    private class ServerDiffCallback : DiffUtil.ItemCallback<VPNServer>() {
        override fun areItemsTheSame(oldItem: VPNServer, newItem: VPNServer): Boolean {
            return oldItem.id == newItem.id
        }
        
        override fun areContentsTheSame(oldItem: VPNServer, newItem: VPNServer): Boolean {
            return oldItem == newItem
        }
    }
}
