package com.vpnturbo.app.model

import android.os.Parcelable
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.Parcelize

@Parcelize
data class VPNServer(
    @SerializedName("id")
    val id: String,
    
    @SerializedName("name")
    val name: String,
    
    @SerializedName("country")
    val country: String,
    
    @SerializedName("countryCode")
    val countryCode: String,
    
    @SerializedName("city")
    val city: String,
    
    @SerializedName("host")
    val host: String,
    
    @SerializedName("port")
    val port: Int,
    
    @SerializedName("protocol")
    val protocol: String = "udp",
    
    @SerializedName("username")
    val username: String? = null,
    
    @SerializedName("password")
    val password: String? = null,
    
    @SerializedName("ovpnConfig")
    val ovpnConfig: String? = null,
    
    @SerializedName("isPremium")
    val isPremium: Boolean = false,
    
    @SerializedName("ping")
    var ping: Int = -1,
    
    @SerializedName("load")
    val load: Int = 0,
    
    @SerializedName("isOnline")
    val isOnline: Boolean = true,
    
    @SerializedName("flagUrl")
    val flagUrl: String? = null
) : Parcelable {
    fun getDisplayName(): String {
        return "$city, $country"
    }
    
    fun getServerType(): String {
        return if (isPremium) "Premium" else "Free"
    }
    
    fun getPingDisplay(): String {
        return if (ping > 0) "${ping}ms" else "N/A"
    }
    
    fun getSignalStrength(): Int {
        return when {
            ping < 0 -> 0
            ping < 50 -> 4
            ping < 100 -> 3
            ping < 200 -> 2
            else -> 1
        }
    }
}

enum class ConnectionStatus {
    DISCONNECTED,
    CONNECTING,
    CONNECTED,
    DISCONNECTING,
    ERROR
}

data class ConnectionStats(
    val sessionTime: Long = 0,
    val bytesIn: Long = 0,
    val bytesOut: Long = 0,
    val connectedServer: VPNServer? = null
) {
    fun getFormattedSessionTime(): String {
        val hours = sessionTime / 3600
        val minutes = (sessionTime % 3600) / 60
        val seconds = sessionTime % 60
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }
    
    fun getFormattedDataUsage(): String {
        val totalBytes = bytesIn + bytesOut
        return when {
            totalBytes < 1024 -> "${totalBytes} B"
            totalBytes < 1024 * 1024 -> "${totalBytes / 1024} KB"
            totalBytes < 1024 * 1024 * 1024 -> "${totalBytes / (1024 * 1024)} MB"
            else -> "${totalBytes / (1024 * 1024 * 1024)} GB"
        }
    }
}
