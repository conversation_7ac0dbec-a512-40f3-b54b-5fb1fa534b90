package com.vpnturbo.app.data

import com.vpnturbo.app.model.VPNServer
import kotlinx.coroutines.delay

class ServerRepository {
    
    // Mock free servers data
    private val freeServers = listOf(
        VPNServer(
            id = "us_free_1",
            name = "US Free Server 1",
            country = "United States",
            countryCode = "US",
            city = "New York",
            host = "us1.freevpn.com",
            port = 1194,
            isPremium = false,
            ping = 45
        ),
        VPNServer(
            id = "uk_free_1",
            name = "UK Free Server 1",
            country = "United Kingdom",
            countryCode = "GB",
            city = "London",
            host = "uk1.freevpn.com",
            port = 1194,
            isPremium = false,
            ping = 78
        ),
        VPNServer(
            id = "de_free_1",
            name = "DE Free Server 1",
            country = "Germany",
            countryCode = "DE",
            city = "Frankfurt",
            host = "de1.freevpn.com",
            port = 1194,
            isPremium = false,
            ping = 92
        ),
        VPNServer(
            id = "jp_free_1",
            name = "JP Free Server 1",
            country = "Japan",
            countryCode = "JP",
            city = "Tokyo",
            host = "jp1.freevpn.com",
            port = 1194,
            isPremium = false,
            ping = 156
        ),
        VPNServer(
            id = "sg_free_1",
            name = "SG Free Server 1",
            country = "Singapore",
            countryCode = "SG",
            city = "Singapore",
            host = "sg1.freevpn.com",
            port = 1194,
            isPremium = false,
            ping = 189
        )
    )
    
    // Mock premium servers data
    private val premiumServers = listOf(
        VPNServer(
            id = "us_premium_1",
            name = "US Premium Server 1",
            country = "United States",
            countryCode = "US",
            city = "Los Angeles",
            host = "us-premium1.vpnturbo.com",
            port = 1194,
            isPremium = true,
            ping = 25
        ),
        VPNServer(
            id = "uk_premium_1",
            name = "UK Premium Server 1",
            country = "United Kingdom",
            countryCode = "GB",
            city = "Manchester",
            host = "uk-premium1.vpnturbo.com",
            port = 1194,
            isPremium = true,
            ping = 35
        )
    )
    
    suspend fun getFreeServers(): List<VPNServer> {
        // Simulate network delay
        delay(1000)
        return freeServers
    }
    
    suspend fun getPremiumServers(): List<VPNServer> {
        // Simulate network delay
        delay(1000)
        return premiumServers
    }
    
    suspend fun getAllServers(): List<VPNServer> {
        return freeServers + premiumServers
    }
    
    suspend fun getServerById(id: String): VPNServer? {
        return getAllServers().find { it.id == id }
    }
    
    suspend fun getFastestServer(): VPNServer? {
        return getAllServers().minByOrNull { it.ping }
    }
    
    fun getOpenVPNConfig(server: VPNServer): String {
        return """
            client
            dev tun
            proto ${server.protocol}
            remote ${server.host} ${server.port}
            resolv-retry infinite
            nobind
            persist-key
            persist-tun
            cipher AES-256-CBC
            auth SHA256
            key-direction 1
            verb 3
            
            <ca>
            -----BEGIN CERTIFICATE-----
            MIIDQjCCAiqgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
            ADA5MQswCQYDVQQGEwJVUzEOMAwGA1UEChMFVlBOVEIxGjAYBgNVBAMTEVZQTiBU
            dXJibyBSb290IENBMB4XDTE5MDcwMjEyMDAwMFoXDTI5MDYyOTEyMDAwMFowOTEL
            MAkGA1UEBhMCVVMxDjAMBgNVBAoTBVZQTlRCMRowGAYDVQQDExFWUE4gVHVyYm8g
            Um9vdCBDQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBANrAG4jKtXlq
            eRlthQRXuOcHdEzXhukVyaSLtfqTTU7U2G22TwFupTY2s4UbZrR/dR52BH/Yf/4g
            sDl6Hd6L+rtVPS2+VtKSiLy3ibVBM666FcplNF8S5IY2ArrxdUu7iRaFUehs1UPV
            yayNSsVvxd8/UkQDmZ7Okt8fMUx1bos8+vqzx+VoYq1fRckLu/lDNz44TH6hmrQR
            T47vDx3lSJiCQrI7t4kMpA2uEhOrEqHRzByVBKBjjBmIdxtB+X4+c6VpXwJ/BPsw
            dTZKMwiPhw4hdQPxQVnyZBfRTB7yjGqC58u5vMZXuE2+OE25+E/arzgxqxSOZWxD
            fqnIGqJTzjsCAwEAAaNTMFEwHQYDVR0OBBYEFK4V3tpE5/tCkuuDmz4Rs+ZJgokq
            MB8GA1UdIwQYMBaAFK4V3tpE5/tCkuuDmz4Rs+ZJgokqMA8GA1UdEwEB/wQFMAMB
            Af8wDQYJKoZIhvcNAQELBQADggEBAGGotaQw0kNuVfenrHzVRgRHoVK8ldkBbCrc
            s/CiuiO7ISjL8btjkqJKWjgPKJoQxfvtlU0nx8GnFn3v5/Q3IcZCEzep1uWcmQwu
            /jzSFpDPfR8XJ9Cy4nqTH4VuaRsVlLyTVcBFRMhLmphWtQUKiB8ts1aNekOzVQH0
            /b6F2/x7R8Yt4jy2JOWZGjvQGruaOBSsFcmLx1BgXPMzYzNvBZ7E5KDEKmmjbGxU
            Gc/ea5TvQa/WFhtoyiRImiNfVphkHdgqxr3z/hSRfLpez/5MJ8hiupGI4FMQfRtl
            SnewPiLrRBEwHBHfNcfvmuPiUX+JWGQZugXiKBQHPXgSohJLqOo=
            -----END CERTIFICATE-----
            </ca>
            
            <cert>
            -----BEGIN CERTIFICATE-----
            MIIDUTCCAjmgAwIBAgIJAMF8OlOsrb8jMA0GCSqGSIb3DQEBCwUAMDkxCzAJBgNV
            BAYTAlVTMQ4wDAYDVQQKEwVWUE5UQjEaMBgGA1UEAxMRVlBOIFR1cmJvIFJvb3Qg
            Q0EwHhcNMTkwNzAyMTIwMDAwWhcNMjkwNjI5MTIwMDAwWjA5MQswCQYDVQQGEwJV
            UzEOMAwGA1UEChMFVlBOVEIxGjAYBgNVBAMTEVZQTiBUdXJibyBDbGllbnQwggEi
            MA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDGGGXzuEQkiM6VXleuugzJQgjv
            MpqR0t/Pc7stQcSGQqmjZbwqU2+wjBLuabot3WMaAcGdBYjGLQxjZm6qiNQ+u+FC
            -----END CERTIFICATE-----
            </cert>
            
            <key>
            -----BEGIN PRIVATE KEY-----
            MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDGGGXzuEQkiM6V
            XleuugzJQgjvMpqR0t/Pc7stQcSGQqmjZbwqU2+wjBLuabot3WMaAcGdBYjGLQxj
            -----END PRIVATE KEY-----
            </key>
        """.trimIndent()
    }
}
